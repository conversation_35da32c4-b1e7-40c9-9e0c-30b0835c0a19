import { useEffect, useCallback } from 'react'
import { message } from 'antd/es'
import { MessageType } from '@src/common/const'
import { ipConnectivity } from '@src/common/utils'
import { log } from '@ht/xlog'
import type { MessageInstance } from 'antd/es/message/interface'
import { extractPageContent } from '@src/common/utils/pageContentExtractor'
import { handleMutationObserver, observerChildElement } from '@src/contents/scripts/injectTranslate'

// 统一的useTranslate hook，专注于UI交互模式
interface UseTranslateProps {
  // 可选参数，用于UI交互模式
  chatUiRef?: React.RefObject<any>
  messageApi?: MessageInstance
}

interface UseTranslateReturn {
  // API模式返回的contextHolder
  contextHolder?: React.ReactNode
  // UI交互模式返回的handleTranslate函数
  handleTranslate?: (pageTitle: string) => Promise<void>
}

const useTranslate = (props?: UseTranslateProps): UseTranslateReturn => {
  const [internalMessageApi, contextHolder] = message.useMessage()
  const messageApiToUse = props?.messageApi || internalMessageApi

  // 获取当前标签页
  const getCurrentTab = useCallback(async (messageApi: MessageInstance) => {
    try {
      const tabs = await chrome.tabs.query({ active: true, currentWindow: true })
      return tabs[0]
    } catch (error) {
      console.error('获取当前标签页失败:', error)
      messageApi.error('获取当前标签页失败')
      return null
    }
  }, [])

  // 刷新当前页面
  const refreshCurrentPage = useCallback(async (tabId: number) => {
    try {
      await chrome.tabs.reload(tabId)
    } catch (error) {
      console.error('刷新页面失败:', error)
    }
  }, [])

  // 记录日志
  const reportLog = useCallback((pageTitle: string) => {
    log({
      id: 'button_click',
      page_id: 'quickReplyBtn',
      page_title: pageTitle,
    })
  }, [])

  // UI交互模式的翻译处理函数
  const handleTranslate = useCallback(async (pageTitle: string) => {
    if (!props?.chatUiRef || !messageApiToUse) {
      console.error('UI交互模式需要提供chatUiRef和messageApi参数')
      return
    }

    const currentTab = await getCurrentTab(messageApiToUse)
    if (!currentTab?.id) {
      messageApiToUse.error('无法获取当前标签页')
      return
    }

    try {
      // 记录日志
      reportLog(pageTitle)

      // 1. 创建新会话
      if (props.chatUiRef.current?.chatContext?.handleNewConversation) {
        const response = await props.chatUiRef.current.chatContext.handleNewConversation()
        console.log('新会话创建成功', response)
        if (response?.code === '0' && response.conversationID) {
          // 处理当前页面所有可见元素
          observerChildElement(document.body)
          handleMutationObserver()
        } else {
          console.error('会话创建失败')
        }
      }

      // if (props.chatUiRef.current?.chatContext?.onSend) {
      //   // 使用chatUI的onSend方法发送消息
      //   await props.chatUiRef.current.chatContext.onSend({
      //     type: 'text',
      //     content: {
      //       text: translateMessage
      //     },
      //     agentId: 'translate' // 指定翻译智能体
      //   })
      //   console.log('翻译消息发送成功，内容长度:', translateMessage.length)
      // }

      // // 4. 同时向content script发送开始翻译的消息（用于页面内翻译功能）
      // await chrome.tabs.sendMessage(currentTab.id, {
      //   type: MessageType.START_TRANSLATE,
      //   data: { pageTitle }
      // })

      // messageApiToUse.success(`翻译请求已发送（页面内容：${pageContentResult.textLength} 字符）`)

    } catch (error) {
      console.error('翻译处理失败:', error)
      if (error.message?.includes('无法提取页面内容')) {
        messageApiToUse.error('无法提取页面内容，请确保页面已完全加载')
      } else {
        messageApiToUse.error('翻译处理失败，请重试')
      }
    }

  }, [props?.chatUiRef, messageApiToUse, reportLog, refreshCurrentPage, getCurrentTab])

  // 初始化网络连接检查
  useEffect(() => {
    ipConnectivity(messageApiToUse)
  }, [messageApiToUse])

  return {
    contextHolder,
    handleTranslate
  }
}

// 导出类型和hook
export type { UseTranslateProps, UseTranslateReturn }
export default useTranslate
